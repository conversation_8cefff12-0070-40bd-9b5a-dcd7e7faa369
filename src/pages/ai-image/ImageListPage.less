@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.ai-image-list-page {
  min-height: 100vh;
  background-color: @common_bg_z0_color;

  // 页面头部样式 - Page header styles
  .page-header {
    background-color: @common_bg_z1_color;
    border-bottom: 1px solid @common_line_light_color;
    padding: 24px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    @media (max-width: 768px) {
      padding: 16px;
      position: relative;
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .header-title {
        h1 {
          font-size: 28px;
          font-weight: 700;
          line-height: 36px;
          color: @common_level1_base_color;
          margin: 0 0 8px 0;

          @media (max-width: 768px) {
            font-size: 24px;
            line-height: 32px;
          }
        }

        p {
          font-size: 14px;
          line-height: 20px;
          color: @common_level3_base_color;
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .create-btn {
          background: linear-gradient(135deg, @theme_primary1_color 0%, @theme_primary2_color 100%);
          border: none;
          height: 44px;
          padding: 0 24px;
          border-radius: @common_border_radius_l;
          font-weight: 600;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          transition: all @common_light_motion_duration @common_light_motion_timing_function;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }

        .refresh-btn {
          height: 44px;
          padding: 0 20px;
          border-radius: @common_border_radius_l;
          border-color: @common_line_light_color;
          color: @common_level2_base_color;
          transition: all @common_light_motion_duration @common_light_motion_timing_function;

          &:hover {
            border-color: @theme_primary1_color;
            color: @theme_primary1_color;
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  // 页面内容样式 - Page content styles
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 120px);

    @media (max-width: 768px) {
      min-height: calc(100vh - 100px);
    }

    // 覆盖 ImageList 组件的样式 - Override ImageList component styles
    .image-list {
      padding: 0;

      @media (max-width: 768px) {
        padding-bottom: 80px; // 为移动端创建按钮留出空间 - Leave space for mobile create button
      }

      // 隐藏原有的头部，因为我们有自己的页面头部 - Hide original header as we have our own page header
      .list-header {
        display: none;
      }

      // 调整图片网格的间距 - Adjust image grid spacing
      .image-grid {
        padding: 24px;
        gap: 24px;

        @media (max-width: 768px) {
          padding: 16px;
          gap: 16px;
        }
      }

      // 移动端创建按钮样式优化 - Mobile create button style optimization
      .mobile-create-btn {
        .create-new-btn {
          background: linear-gradient(135deg, @theme_primary1_color 0%, @theme_primary2_color 100%);
          border: none;
          font-weight: 600;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
        }
      }

      // 空状态样式优化 - Empty state style optimization
      .empty-state {
        padding: 60px 24px;

        .empty-icon {
          font-size: 80px;
          margin-bottom: 32px;
        }

        .empty-title {
          font-size: 20px;
          margin-bottom: 16px;
        }

        .empty-description {
          font-size: 16px;
          margin-bottom: 40px;
          max-width: 400px;
        }

        .create-btn {
          background: linear-gradient(135deg, @theme_primary1_color 0%, @theme_primary2_color 100%);
          border: none;
          width: 240px;
          height: 52px;
          font-size: 16px;
          font-weight: 600;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
        }
      }

      // 错误状态样式优化 - Error state style optimization
      .error-state {
        padding: 60px 24px;

        .error-title {
          font-size: 20px;
          margin-bottom: 16px;
        }

        .error-description {
          font-size: 16px;
          margin-bottom: 32px;
          max-width: 400px;
        }

        .retry-btn {
          width: 160px;
          height: 44px;
          font-size: 16px;
        }
      }

      // 加载更多按钮样式优化 - Load more button style optimization
      .load-more {
        padding: 32px 24px;

        .load-more-btn {
          width: 240px;
          height: 44px;
          border-radius: @common_border_radius_l;
          border-color: @common_line_light_color;
          color: @common_level2_base_color;
          transition: all @common_light_motion_duration @common_light_motion_timing_function;

          &:hover {
            border-color: @theme_primary1_color;
            color: @theme_primary1_color;
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// 响应式设计优化 - Responsive design optimization
@media (max-width: 480px) {
  .ai-image-list-page {
    .page-header {
      .header-content {
        .header-title {
          h1 {
            font-size: 20px;
            line-height: 28px;
          }
        }
      }
    }

    .page-content {
      .image-list {
        .image-grid {
          padding: 12px;
          gap: 12px;
        }
      }
    }
  }
}

// 深色模式支持 - Dark mode support
@media (prefers-color-scheme: dark) {
  .ai-image-list-page {
    background-color: #1a1a1a;

    .page-header {
      background-color: #2a2a2a;
      border-bottom-color: #3a3a3a;
    }
  }
}

// 动画效果 - Animation effects
.ai-image-list-page {
  .page-header {
    animation: slideDown 0.3s ease-out;
  }

  .page-content {
    animation: fadeIn 0.5s ease-out 0.1s both;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
