// Mock 数据示例 - Mock data examples for testing the updated ImageList component

export interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组 - Array containing 4 images
  originImgUrl?: string;
  type: 'normal' | 'hd';
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean; // 添加点踩状态 - Add dislike state
}

// Mock 图片 URLs - Mock image URLs for testing
const mockImageUrls = [
  'https://picsum.photos/400/400?random=1',
  'https://picsum.photos/400/400?random=2',
  'https://picsum.photos/400/400?random=3',
  'https://picsum.photos/400/400?random=4',
];

const mockImageUrls2 = [
  'https://picsum.photos/400/400?random=5',
  'https://picsum.photos/400/400?random=6',
  'https://picsum.photos/400/400?random=7',
  'https://picsum.photos/400/400?random=8',
];

// Mock 数据：生成中状态 - Mock data: generating state
export const mockGeneratingImage: ImageInfo = {
  uuid: 'generating-001',
  imgUrls: [],
  type: 'normal',
  status: 'processing',
  createAt: new Date().toISOString(),
  prompt: '一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影',
  size: '1024x1024',
  progress: 75, // 75% 进度用于演示
  liked: false,
  disliked: false,
};

// Mock 数据：已完成状态 - Mock data: completed state
export const mockCompletedImage: ImageInfo = {
  uuid: 'completed-001',
  imgUrls: mockImageUrls,
  type: 'normal',
  status: 'completed',
  createAt: new Date(Date.now() - 300000).toISOString(), // 5分钟前
  finishTime: new Date(Date.now() - 240000).toISOString(), // 4分钟前
  prompt: '美丽的山水风景，中国水墨画风格，意境深远',
  size: '1024x1024',
  liked: true,
  disliked: false,
};

// Mock 数据：已完成状态（被点踩）- Mock data: completed state (disliked)
export const mockDislikedImage: ImageInfo = {
  uuid: 'completed-002',
  imgUrls: mockImageUrls2,
  type: 'normal',
  status: 'completed',
  createAt: new Date(Date.now() - 600000).toISOString(), // 10分钟前
  finishTime: new Date(Date.now() - 540000).toISOString(), // 9分钟前
  prompt: '现代城市夜景，霓虹灯闪烁，赛博朋克风格',
  size: '1024x1024',
  liked: false,
  disliked: true,
};

// Mock 数据：失败状态 - Mock data: failed state
export const mockFailedImage: ImageInfo = {
  uuid: 'failed-001',
  imgUrls: [],
  type: 'normal',
  status: 'failed',
  createAt: new Date(Date.now() - 900000).toISOString(), // 15分钟前
  errorMsg: '生成超时，请重试',
  prompt: '抽象艺术作品，色彩丰富，现代风格',
  size: '1024x1024',
  liked: false,
  disliked: false,
};

// Mock 数据列表 - Mock data list
export const mockImageList: ImageInfo[] = [
  mockGeneratingImage,
  mockCompletedImage,
  mockDislikedImage,
  mockFailedImage,
];

// Mock 进度映射 - Mock progress mapping
export const mockProgressMap: Record<string, number> = {
  'generating-001': 75,
};

// 模拟轮询更新进度的函数 - Function to simulate polling progress updates
export const simulateProgressUpdate = (
  imageUuid: string,
  currentProgress: number,
  onProgressUpdate: (uuid: string, progress: number) => void
): NodeJS.Timeout => {
  return setInterval(() => {
    const newProgress = Math.min(100, currentProgress + Math.random() * 10);
    onProgressUpdate(imageUuid, newProgress);
    
    if (newProgress >= 100) {
      // 模拟生成完成，更新状态为 completed
      // Simulate generation completion, update status to completed
      setTimeout(() => {
        // 这里应该调用父组件的更新函数来更新图片状态
        // Here should call parent component's update function to update image status
        console.log(`Image ${imageUuid} generation completed!`);
      }, 1000);
    }
  }, 2000); // 每2秒更新一次进度 - Update progress every 2 seconds
};

// 使用示例 - Usage example
/*
import { mockImageList, mockProgressMap, simulateProgressUpdate } from './mock-data';

// 在组件中使用
const [imageList, setImageList] = useState(mockImageList);
const [progressMap, setProgressMap] = useState(mockProgressMap);

// 模拟进度更新
useEffect(() => {
  const intervals: NodeJS.Timeout[] = [];
  
  imageList.forEach(image => {
    if (image.status === 'processing') {
      const interval = simulateProgressUpdate(
        image.uuid,
        progressMap[image.uuid] || 0,
        (uuid, progress) => {
          setProgressMap(prev => ({ ...prev, [uuid]: progress }));
        }
      );
      intervals.push(interval);
    }
  });
  
  return () => {
    intervals.forEach(interval => clearInterval(interval));
  };
}, [imageList]);
*/
