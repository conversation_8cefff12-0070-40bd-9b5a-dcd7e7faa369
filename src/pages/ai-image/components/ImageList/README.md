# ImageList 组件更新说明

## 概述

ImageList 组件已按照设计稿要求进行了全面更新，实现了类似 Midjourney 的图片生成效果。

## 主要更新内容

### 1. 图片生成逻辑
- **4张图片生成**：每次生成4张图片，以2x2网格形式展示
- **渐变效果**：生成过程中显示从模糊到清晰的渐变效果
- **轮询机制**：支持轮询查询生成状态接口

### 2. UI 界面更新

#### 生成过程中
- 文字提示：`"正在根据你的要求生成图片... 75%"`
- 圆形进度条：显示实时数字进度
- 4个占位符：根据进度显示不同程度的模糊效果

#### 生成完成后
- 文字提示：`"已完成，请查看"`
- 4张图片：2x2网格布局展示
- 操作按钮：点赞、点踩、重新生成（icon形式）

### 3. 新增功能
- **点踩功能**：新增点踩按钮和相关状态管理
- **圆形进度条**：自定义SVG圆形进度条组件
- **模糊渐变效果**：根据进度动态计算模糊值

## 组件接口更新

### ImageInfo 接口
```typescript
interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组
  // ... 其他字段
  liked?: boolean;
  disliked?: boolean; // 新增点踩状态
}
```

### 新增组件
```typescript
// 圆形进度条组件
const CircularProgress: React.FC<{progress: number}> = ({ progress }) => {
  // SVG圆形进度条实现
};
```

## 样式更新

### 主要样式类
- `.image-grid-completed` - 完成状态的4张图片网格
- `.image-grid-generating` - 生成中的4个占位符
- `.circular-progress` - 圆形进度条样式
- `.completion-text` - "已完成，请查看" 文字样式

### 操作按钮样式
- `.like-btn.liked` - 点赞激活状态
- `.dislike-btn.disliked` - 点踩激活状态
- `.regenerate-btn` - 重新生成按钮

## 使用方法

### 1. 基本使用
```tsx
import ImageList from './components/ImageList';
import { mockImageList, mockProgressMap } from './components/ImageList/mock-data';

<ImageList
  imageList={imageList}
  isLoading={false}
  error={null}
  onCreateNew={handleCreateNew}
  onRegenerate={handleRegenerate}
  onRefresh={handleRefresh}
  progressMap={progressMap}
  hasNextPage={false}
  isLoadingMore={false}
  onLoadMore={handleLoadMore}
/>
```

### 2. Mock 数据测试
```tsx
import { 
  mockImageList, 
  mockProgressMap, 
  simulateProgressUpdate 
} from './components/ImageList/mock-data';

// 使用 Mock 数据进行测试
const [imageList, setImageList] = useState(mockImageList);
const [progressMap, setProgressMap] = useState(mockProgressMap);
```

### 3. 进度更新
```tsx
// 模拟轮询更新进度
useEffect(() => {
  const intervals: NodeJS.Timeout[] = [];
  
  imageList.forEach(image => {
    if (image.status === 'processing') {
      const interval = simulateProgressUpdate(
        image.uuid,
        progressMap[image.uuid] || 0,
        (uuid, progress) => {
          setProgressMap(prev => ({ ...prev, [uuid]: progress }));
        }
      );
      intervals.push(interval);
    }
  });
  
  return () => {
    intervals.forEach(interval => clearInterval(interval));
  };
}, [imageList]);
```

## 技术实现细节

### 1. 模糊渐变算法
```typescript
const getBlurValue = (imageIndex: number, progress: number): number => {
  const imageProgress = Math.max(0, Math.min(100, (progress - imageIndex * 25) * 4));
  return Math.max(0, 20 - (imageProgress / 100) * 20);
};
```

### 2. 圆形进度条实现
- 使用SVG circle元素
- 通过 `stroke-dasharray` 和 `stroke-dashoffset` 控制进度
- CSS transform 旋转-90度使进度从顶部开始

### 3. 响应式设计
- 移动端和PC端适配
- 网格布局自动调整
- 按钮大小和间距优化

## 注意事项

1. **图片数组**：确保 `imgUrls` 数组包含4张图片
2. **进度范围**：progress 值应在 0-100 之间
3. **状态管理**：liked 和 disliked 状态互斥
4. **性能优化**：大量图片时注意内存使用
5. **错误处理**：网络错误和API错误的友好提示

## 兼容性

- 保持与现有API接口的兼容性
- 向后兼容原有的单张图片显示
- 渐进式增强，不影响现有功能
