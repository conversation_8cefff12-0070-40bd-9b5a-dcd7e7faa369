import React, { useState } from 'react';
import { But<PERSON>, Toast, ProgressBar } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  UploadTwoOutlined,
  RefreshOutlined as ReloadOutlined,
  HDLOutlined,
  AddToSFilled,
  RefreshOutlined,
} from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import OptimizedImage from '@/components/OptimizedImage';
import { generateHDImage, rateImage, reGenerateImage } from '@/apis/image';
import './index.less';

// Image information interface (should match the one in main page)
interface ImageInfo {
  uuid: string;
  imgUrls: string[];
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
}

interface ImageListProps {
  imageList: ImageInfo[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate: (imageInfo: ImageInfo) => void;
  onRefresh: () => void;
  progressMap: Record<string, number>;
  hasNextPage: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  loadImageList: (isLoadMore?: boolean) => void;
}

const ImageList: React.FC<ImageListProps> = ({
  imageList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  progressMap,
  hasNextPage,
  isLoadingMore,
  onLoadMore,
  loadImageList,
}) => {
  const [generatingHD, setGeneratingHD] = useState<Record<string, boolean>>({});
  const isMobile = isMobileDevice();

  // Handle like/unlike
  const handleLike = async (imageInfo: ImageInfo) => {
    try {
      const newLikedState = !imageInfo.liked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newLikedState ? 'like' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newLikedState ? '已点赞' : '已取消点赞',
        position: 'top',
        duration: 1,
      });
    } catch (error) {
      Toast.fail({
        content: '操作失败，请重试',
        position: 'top',
        duration: 2,
      });
    }
  };

  // Handle download
  const handleDownload = (imageInfo: ImageInfo) => {
    const downloadUrl = imageInfo.type === 'hd' ? imageInfo.originImgUrl : imageInfo.imgUrls?.[0];
    if (!downloadUrl) return;

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `ai-image-${imageInfo.uuid}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Toast.success({
      content: '开始下载',
      position: 'top',
      duration: 1,
    });
  };

  // Handle HD generation
  const handleGenerateHD = async (imageInfo: ImageInfo) => {
    if (generatingHD[imageInfo.uuid] || imageInfo.type === 'hd') return;

    setGeneratingHD((prev) => ({ ...prev, [imageInfo.uuid]: true }));

    try {
      const result = await generateHDImage({
        imgUrl: imageInfo.imgUrls?.[0] || '',
        imgLevel: '2',
      });

      if (result && result.success && result.data) {
        Toast.success({
          content: '高清图片生成成功',
          position: 'top',
          duration: 2,
        });

        // Refresh the image list to get updated HD URL
        loadImageList();
      } else {
        throw new Error(result?.errorMsg || '高清图片生成失败');
      }
    } catch (error) {
      Toast.fail({
        content: error instanceof Error ? error.message : '高清图片生成失败',
        position: 'top',
        duration: 3,
      });
    } finally {
      setGeneratingHD((prev) => ({ ...prev, [imageInfo.uuid]: false }));
    }
  };

  // Render image item
  const renderImageItem = (imageInfo: ImageInfo) => {
    const progress = progressMap[imageInfo.uuid] || 0;
    const isGenerating = imageInfo.status === 'pending' || imageInfo.status === 'processing';
    const isFailed = imageInfo.status === 'failed';
    const isCompleted = imageInfo.status === 'completed';

    return (
      <div key={imageInfo.uuid} className="image-item">
        <div className="image-container">
          {isCompleted && (
            <OptimizedImage
              src={imageInfo.imgUrls?.[0] || ''}
              alt="Generated image"
              className="image-preview"
            />
          )}

          {isGenerating && (
            <div className="image-generating">
              <div className="generating-overlay">
                <ProgressBar
                  percent={progress}
                />
                <div className="generating-text">
                  正在生成图片...{progress}%
                </div>
              </div>
            </div>
          )}

          {isFailed && (
            <div className="image-failed">
              <div className="failed-overlay">
                <div className="failed-text">生成失败</div>
                <div className="failed-reason">{imageInfo.errorMsg || '未知错误'}</div>
              </div>
            </div>
          )}
        </div>

        {/* Image info */}
        <div className="image-info">
          <div className="image-prompt">{imageInfo.prompt}</div>
          <div className="image-meta">
            <span className="image-size">{imageInfo.size}</span>
            <span className="image-time">
              {new Date(imageInfo.createAt).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Action buttons */}
        {isCompleted && (
          <div className="image-actions">
            <Button
              size="small"
              className={`action-btn like-btn ${imageInfo.liked ? 'liked' : ''}`}
              onClick={() => handleLike(imageInfo)}
            >
              {imageInfo.liked ? <LikeFilled /> : <LikeOutlined />}
            </Button>

            <Button
              size="small"
              className="action-btn download-btn"
              onClick={() => handleDownload(imageInfo)}
            >
              <UploadTwoOutlined />
            </Button>

            {imageInfo.type !== 'hd' && (
              <Button
                size="small"
                className="action-btn hd-btn"
                loading={generatingHD[imageInfo.uuid]}
                onClick={() => handleGenerateHD(imageInfo)}
              >
                <HDLOutlined />
              </Button>
            )}

            <Button
              size="small"
              className="action-btn regenerate-btn"
              onClick={() => onRegenerate(imageInfo)}
            >
              <ReloadOutlined />
            </Button>
          </div>
        )}
      </div>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <div className="empty-state">
      <div className="empty-icon">
        <AddToSFilled />
      </div>
      <div className="empty-title">还没有生成的图片</div>
      <div className="empty-description">
        点击下方按钮开始创建您的第一张AI图片
      </div>
      <Button
        type="primary"
        size="large"
        className="create-btn"
        onClick={onCreateNew}
      >
        开始创建
      </Button>
    </div>
  );

  // Render error state
  const renderErrorState = () => (
    <div className="error-state">
      <div className="error-title">加载失败</div>
      <div className="error-description">{error}</div>
      <Button
        type="primary"
        size="middle"
        className="retry-btn"
        onClick={onRefresh}
      >
        <RefreshOutlined />
        重试
      </Button>
    </div>
  );

  if (error) {
    return (
      <div className="image-list">
        {renderErrorState()}
      </div>
    );
  }

  if (!isLoading && imageList.length === 0) {
    return (
      <div className="image-list">
        {renderEmptyState()}
      </div>
    );
  }

  return (
    <div className="image-list">
      {/* Header */}
      <div className="list-header">
        <div className="header-title">生成历史</div>
        <Button
          size="small"
          className="refresh-btn"
          onClick={onRefresh}
        >
          <RefreshOutlined />
        </Button>
      </div>

      {/* Image grid */}
      <div className="image-grid">
        {imageList.map(renderImageItem)}
      </div>

      {/* Load more */}
      {hasNextPage && (
        <div className="load-more">
          <Button
            size="large"
            loading={isLoadingMore}
            onClick={onLoadMore}
            className="load-more-btn"
          >
            {isLoadingMore ? '加载中...' : '加载更多'}
          </Button>
        </div>
      )}

      {/* Create new button for mobile */}
      {isMobile && (
        <div className="mobile-create-btn">
          <Button
            type="primary"
            size="large"
            onClick={onCreateNew}
            className="create-new-btn"
          >
            <AddToSFilled />
            创建新图片
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageList;
