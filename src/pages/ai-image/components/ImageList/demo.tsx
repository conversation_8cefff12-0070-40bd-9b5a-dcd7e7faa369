// Demo 页面 - Demo page for testing the updated ImageList component
import React, { useState, useEffect } from 'react';
import ImageList from './index';
import { 
  mockImageList, 
  mockProgressMap, 
  simulateProgressUpdate,
  type ImageInfo 
} from './mock-data';

const ImageListDemo: React.FC = () => {
  const [imageList, setImageList] = useState<ImageInfo[]>(mockImageList);
  const [progressMap, setProgressMap] = useState<Record<string, number>>(mockProgressMap);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 模拟进度更新 - Simulate progress updates
  useEffect(() => {
    const intervals: NodeJS.Timeout[] = [];
    
    imageList.forEach(image => {
      if (image.status === 'processing') {
        const interval = simulateProgressUpdate(
          image.uuid,
          progressMap[image.uuid] || 0,
          (uuid, progress) => {
            setProgressMap(prev => ({ ...prev, [uuid]: progress }));
            
            // 当进度达到100%时，更新图片状态为完成
            // When progress reaches 100%, update image status to completed
            if (progress >= 100) {
              setTimeout(() => {
                setImageList(prevList => 
                  prevList.map(img => 
                    img.uuid === uuid 
                      ? { 
                          ...img, 
                          status: 'completed',
                          finishTime: new Date().toISOString(),
                          imgUrls: [
                            'https://picsum.photos/400/400?random=10',
                            'https://picsum.photos/400/400?random=11',
                            'https://picsum.photos/400/400?random=12',
                            'https://picsum.photos/400/400?random=13',
                          ]
                        }
                      : img
                  )
                );
              }, 1000);
            }
          }
        );
        intervals.push(interval);
      }
    });
    
    return () => {
      intervals.forEach(interval => clearInterval(interval));
    };
  }, [imageList, progressMap]);

  // 处理创建新图片 - Handle create new image
  const handleCreateNew = () => {
    const newImage: ImageInfo = {
      uuid: `new-${Date.now()}`,
      imgUrls: [],
      type: 'normal',
      status: 'processing',
      createAt: new Date().toISOString(),
      prompt: '新生成的图片，测试用例',
      size: '1024x1024',
      progress: 0,
      liked: false,
      disliked: false,
    };

    setImageList(prev => [newImage, ...prev]);
    setProgressMap(prev => ({ ...prev, [newImage.uuid]: 0 }));
  };

  // 处理重新生成 - Handle regenerate
  const handleRegenerate = (imageInfo: ImageInfo) => {
    const regeneratedImage: ImageInfo = {
      ...imageInfo,
      uuid: `regen-${Date.now()}`,
      status: 'processing',
      createAt: new Date().toISOString(),
      finishTime: undefined,
      imgUrls: [],
      progress: 0,
      liked: false,
      disliked: false,
    };

    setImageList(prev => [regeneratedImage, ...prev]);
    setProgressMap(prev => ({ ...prev, [regeneratedImage.uuid]: 0 }));
  };

  // 处理刷新 - Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setError(null);
    }, 1000);
  };

  // 处理加载更多 - Handle load more
  const handleLoadMore = () => {
    console.log('Load more images...');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>ImageList 组件演示</h1>
      <p>这是更新后的 ImageList 组件演示，展示了类似 Midjourney 的图片生成效果。</p>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleCreateNew}
          style={{
            padding: '10px 20px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          创建新图片
        </button>
        
        <button 
          onClick={handleRefresh}
          style={{
            padding: '10px 20px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          刷新列表
        </button>
      </div>

      <ImageList
        imageList={imageList}
        isLoading={isLoading}
        error={error}
        onCreateNew={handleCreateNew}
        onRegenerate={handleRegenerate}
        onRefresh={handleRefresh}
        progressMap={progressMap}
        hasNextPage={false}
        isLoadingMore={false}
        onLoadMore={handleLoadMore}
      />

      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>功能说明</h3>
        <ul>
          <li><strong>生成中状态</strong>：显示"正在根据你的要求生成图片... X%"和圆形进度条</li>
          <li><strong>模糊渐变</strong>：4个占位符根据进度显示不同程度的模糊效果</li>
          <li><strong>完成状态</strong>：显示"已完成，请查看"和4张图片的2x2网格</li>
          <li><strong>操作按钮</strong>：点赞、点踩、重新生成三个按钮</li>
          <li><strong>状态管理</strong>：点赞和点踩状态互斥</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageListDemo;
