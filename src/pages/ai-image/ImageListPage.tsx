// AI 图片生成列表页 - AI Image Generation List Page
import React, { useState, useEffect, useCallback } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import { EditOutlined, RefreshOutlined } from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import ImageList from './components/ImageList';
import './ImageListPage.less';

// 图片信息接口 - Image information interface
interface ImageInfo {
  uuid: string;
  imgUrls: string[];
  originImgUrl?: string;
  type: 'normal' | 'hd';
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean;
}

// Mock 图片 URLs - Mock image URLs
const generateMockImageUrls = (seed: number) => [
  `https://picsum.photos/400/400?random=${seed}`,
  `https://picsum.photos/400/400?random=${seed + 1}`,
  `https://picsum.photos/400/400?random=${seed + 2}`,
  `https://picsum.photos/400/400?random=${seed + 3}`,
];

// Mock 提示词列表 - Mock prompt list
const mockPrompts = [
  '一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影',
  '美丽的山水风景，中国水墨画风格，意境深远',
  '现代城市夜景，霓虹灯闪烁，赛博朋克风格',
  '古典欧式建筑，巴洛克风格，金色装饰',
  '抽象艺术作品，色彩丰富，现代风格',
  '宇宙星空，银河系，深邃神秘',
  '日式庭院，樱花飞舞，宁静优雅',
  '蒸汽朋克机械装置，复古未来主义',
];

const ImageListPage: React.FC = () => {
  const [imageList, setImageList] = useState<ImageInfo[]>([]);
  const [progressMap, setProgressMap] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const isMobile = isMobileDevice();

  // 生成随机 UUID - Generate random UUID
  const generateUUID = () => `img-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // 创建初始 Mock 数据 - Create initial mock data
  const createInitialMockData = useCallback((): ImageInfo[] => {
    const now = Date.now();
    return [
      // 生成中的图片 - Generating image
      {
        uuid: generateUUID(),
        imgUrls: [],
        type: 'normal',
        status: 'processing',
        createAt: new Date(now - 30000).toISOString(),
        prompt: mockPrompts[0],
        size: '1024x1024',
        progress: 45,
        liked: false,
        disliked: false,
      },
      // 已完成的图片（点赞）- Completed image (liked)
      {
        uuid: generateUUID(),
        imgUrls: generateMockImageUrls(10),
        type: 'normal',
        status: 'completed',
        createAt: new Date(now - 300000).toISOString(),
        finishTime: new Date(now - 240000).toISOString(),
        prompt: mockPrompts[1],
        size: '1024x1024',
        liked: true,
        disliked: false,
      },
      // 已完成的图片（点踩）- Completed image (disliked)
      {
        uuid: generateUUID(),
        imgUrls: generateMockImageUrls(20),
        type: 'normal',
        status: 'completed',
        createAt: new Date(now - 600000).toISOString(),
        finishTime: new Date(now - 540000).toISOString(),
        prompt: mockPrompts[2],
        size: '1024x1024',
        liked: false,
        disliked: true,
      },
      // 失败的图片 - Failed image
      {
        uuid: generateUUID(),
        imgUrls: [],
        type: 'normal',
        status: 'failed',
        createAt: new Date(now - 900000).toISOString(),
        errorMsg: '生成超时，请重试',
        prompt: mockPrompts[3],
        size: '1024x1024',
        liked: false,
        disliked: false,
      },
      // 更多已完成的图片 - More completed images
      {
        uuid: generateUUID(),
        imgUrls: generateMockImageUrls(30),
        type: 'normal',
        status: 'completed',
        createAt: new Date(now - 1200000).toISOString(),
        finishTime: new Date(now - 1140000).toISOString(),
        prompt: mockPrompts[4],
        size: '1024x1024',
        liked: false,
        disliked: false,
      },
    ];
  }, []);

  // 初始化数据 - Initialize data
  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      const initialData = createInitialMockData();
      setImageList(initialData);
      
      // 设置生成中图片的初始进度 - Set initial progress for generating images
      const initialProgressMap: Record<string, number> = {};
      initialData.forEach((img) => {
        if (img.status === 'processing') {
          initialProgressMap[img.uuid] = img.progress || 0;
        }
      });
      setProgressMap(initialProgressMap);

      setIsLoading(false);
    }, 1000);
  }, [createInitialMockData]);

  // 模拟进度更新 - Simulate progress updates
  useEffect(() => {
    const intervals: Array<ReturnType<typeof setInterval>> = [];

    imageList.forEach((image) => {
      if (image.status === 'processing') {
        const interval = setInterval(() => {
          setProgressMap((prev) => {
            const currentProgress = prev[image.uuid] || 0;
            const newProgress = Math.min(100, currentProgress + Math.random() * 15 + 5);

            // 当进度达到100%时，更新图片状态为完成 - When progress reaches 100%, update status to completed
            if (newProgress >= 100) {
              setTimeout(() => {
                setImageList((prevList) =>
                  prevList.map((img) =>
                    img.uuid === image.uuid
                      ? {
                        ...img,
                        status: 'completed',
                        finishTime: new Date().toISOString(),
                        imgUrls: generateMockImageUrls(Math.floor(Math.random() * 100) + 50),
                      }
                      : img,
                  ),
                );

                Toast.success({
                  content: '图片生成完成！',
                  position: 'top',
                  duration: 2,
                });
              }, 1000);
            }

            return { ...prev, [image.uuid]: newProgress };
          });
        }, 2000); // 每2秒更新一次进度 - Update progress every 2 seconds

        intervals.push(interval);
      }
    });

    return () => {
      intervals.forEach((interval) => clearInterval(interval));
    };
  }, [imageList]);

  // 处理创建新图片 - Handle create new image
  const handleCreateNew = () => {
    const randomPrompt = mockPrompts[Math.floor(Math.random() * mockPrompts.length)];
    const newImage: ImageInfo = {
      uuid: generateUUID(),
      imgUrls: [],
      type: 'normal',
      status: 'processing',
      createAt: new Date().toISOString(),
      prompt: randomPrompt,
      size: '1024x1024',
      progress: 0,
      liked: false,
      disliked: false,
    };

    setImageList((prev) => [newImage, ...prev]);
    setProgressMap((prev) => ({ ...prev, [newImage.uuid]: 0 }));

    Toast.success({
      content: '开始生成新图片...',
      position: 'top',
      duration: 2,
    });
  };

  // 处理重新生成 - Handle regenerate
  const handleRegenerate = (imageInfo: ImageInfo) => {
    const regeneratedImage: ImageInfo = {
      ...imageInfo,
      uuid: generateUUID(),
      status: 'processing',
      createAt: new Date().toISOString(),
      finishTime: undefined,
      imgUrls: [],
      progress: 0,
      liked: false,
      disliked: false,
    };

    setImageList(prev => [regeneratedImage, ...prev]);
    setProgressMap(prev => ({ ...prev, [regeneratedImage.uuid]: 0 }));
    
    Toast.success({
      content: '开始重新生成图片...',
      position: 'top',
      duration: 2,
    });
  };

  // 处理刷新 - Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);
    
    setTimeout(() => {
      const refreshedData = createInitialMockData();
      setImageList(refreshedData);
      
      const refreshedProgressMap: Record<string, number> = {};
      refreshedData.forEach(img => {
        if (img.status === 'processing') {
          refreshedProgressMap[img.uuid] = img.progress || 0;
        }
      });
      setProgressMap(refreshedProgressMap);
      
      setIsLoading(false);
      
      Toast.success({
        content: '列表已刷新',
        position: 'top',
        duration: 1,
      });
    }, 1000);
  };

  // 处理加载更多 - Handle load more
  const handleLoadMore = () => {
    if (isLoadingMore) return;
    
    setIsLoadingMore(true);
    
    setTimeout(() => {
      const moreImages: ImageInfo[] = Array.from({ length: 3 }, (_, index) => ({
        uuid: generateUUID(),
        imgUrls: generateMockImageUrls(Math.floor(Math.random() * 100) + 100),
        type: 'normal' as const,
        status: 'completed',
        createAt: new Date(Date.now() - (index + 1) * 1800000).toISOString(),
        finishTime: new Date(Date.now() - (index + 1) * 1800000 + 60000).toISOString(),
        prompt: mockPrompts[Math.floor(Math.random() * mockPrompts.length)],
        size: '1024x1024',
        liked: false,
        disliked: false,
      }));
      
      setImageList(prev => [...prev, ...moreImages]);
      setIsLoadingMore(false);
      
      // 模拟没有更多数据 - Simulate no more data
      if (imageList.length > 15) {
        setHasNextPage(false);
      }
    }, 1500);
  };

  return (
    <div className="ai-image-list-page">
      {/* 页面头部 - Page header */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-title">
            <h1>AI 图片生成</h1>
            <p>体验类似 Midjourney 的图片生成效果</p>
          </div>
          
          {!isMobile && (
            <div className="header-actions">
              <Button
                type="primary"
                size="large"
                onClick={handleCreateNew}
                className="create-btn"
              >
                <EditOutlined />
                生成新图片
              </Button>
              
              <Button
                size="large"
                onClick={handleRefresh}
                className="refresh-btn"
              >
                <RefreshOutlined />
                刷新
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 图片列表 - Image list */}
      <div className="page-content">
        <ImageList
          imageList={imageList}
          isLoading={isLoading}
          error={error}
          onCreateNew={handleCreateNew}
          onRegenerate={handleRegenerate}
          onRefresh={handleRefresh}
          progressMap={progressMap}
          hasNextPage={hasNextPage}
          isLoadingMore={isLoadingMore}
          onLoadMore={handleLoadMore}
          loadImageList={() => {}} // Mock function for compatibility
        />
      </div>
    </div>
  );
};

export default ImageListPage;
