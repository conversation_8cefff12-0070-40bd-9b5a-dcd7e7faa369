// 路由配置示例 - Route configuration example
// 这个文件展示了如何在项目中添加新的图片列表页面路由

// 示例：在 React Router 中添加路由
/*
import { Routes, Route } from 'react-router-dom';
import AIImagePage from './pages/ai-image';
import AIImageListPage from './pages/ai-image/list';

const AppRoutes = () => {
  return (
    <Routes>
      // 原有的完整功能页面
      <Route path="/ai-image" element={<AIImagePage />} />
      
      // 新增的专门列表页面
      <Route path="/ai-image/list" element={<AIImageListPage />} />
      
      // 其他路由...
    </Routes>
  );
};
*/

// 示例：在 Next.js 中添加页面
/*
// pages/ai-image/index.tsx - 原有页面
export { default } from '../../src/pages/ai-image';

// pages/ai-image/list.tsx - 新增列表页面
export { default } from '../../src/pages/ai-image/list';
*/

// 示例：在 UmiJS 中添加路由配置
/*
// .umirc.ts 或 config/config.ts
export default {
  routes: [
    {
      path: '/ai-image',
      component: '@/pages/ai-image',
      name: 'AI图片生成',
    },
    {
      path: '/ai-image/list',
      component: '@/pages/ai-image/list',
      name: 'AI图片列表',
    },
  ],
};
*/

// 示例：在 Vue Router 中添加路由
/*
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/ai-image',
    name: 'AIImage',
    component: () => import('@/pages/ai-image/index.vue'),
  },
  {
    path: '/ai-image/list',
    name: 'AIImageList',
    component: () => import('@/pages/ai-image/list.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});
*/

// 页面访问说明
export const PAGE_ROUTES = {
  // 完整功能页面（包含表单和列表）
  FULL_PAGE: '/ai-image',
  
  // 专门的列表页面（直接展示列表和Mock数据）
  LIST_PAGE: '/ai-image/list',
} as const;

// 页面功能对比
export const PAGE_FEATURES = {
  FULL_PAGE: {
    name: '完整功能页面',
    path: '/ai-image',
    features: [
      '图片上传表单',
      '参数配置',
      '实时生成',
      '图片列表',
      '历史记录',
    ],
    description: '包含完整的图片生成流程，从上传到生成到查看',
  },
  LIST_PAGE: {
    name: '列表展示页面',
    path: '/ai-image/list',
    features: [
      'Mock数据展示',
      '生成过程模拟',
      '4张图片网格',
      '渐变效果演示',
      '交互功能测试',
    ],
    description: '专门用于展示和测试图片列表功能，包含完整的Mock数据',
  },
} as const;

// 开发环境访问链接
export const DEV_LINKS = {
  // 本地开发环境
  LOCAL: {
    FULL_PAGE: 'http://localhost:3000/ai-image',
    LIST_PAGE: 'http://localhost:3000/ai-image/list',
  },
  
  // 测试环境（根据实际情况修改）
  TEST: {
    FULL_PAGE: 'https://test.example.com/ai-image',
    LIST_PAGE: 'https://test.example.com/ai-image/list',
  },
} as const;

// 使用建议
export const USAGE_RECOMMENDATIONS = {
  DEVELOPMENT: {
    title: '开发阶段',
    recommendation: '使用 /ai-image/list 页面进行UI和交互测试',
    reasons: [
      '无需配置真实API',
      'Mock数据覆盖所有状态',
      '可以快速验证功能',
      '便于演示和调试',
    ],
  },
  
  TESTING: {
    title: '测试阶段',
    recommendation: '使用 /ai-image 页面进行完整流程测试',
    reasons: [
      '测试真实API集成',
      '验证完整用户流程',
      '测试错误处理',
      '性能测试',
    ],
  },
  
  DEMO: {
    title: '演示阶段',
    recommendation: '根据演示目标选择合适页面',
    reasons: [
      '功能演示用完整页面',
      'UI效果演示用列表页面',
      '快速展示用列表页面',
      '详细说明用完整页面',
    ],
  },
} as const;
